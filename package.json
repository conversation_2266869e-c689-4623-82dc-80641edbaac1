{"name": "charging-maintenance-ui", "version": "0.0.1", "description": "Bangdao Back Manage System", "author": "dingxingxing <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "lint": "vue-cli-service lint", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml", "plop": "plop  --plopfile ./src/assets/template/config.js"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "gitlab", "url": "http://gitlab.bangdao-tech.com/bangdao-templates/back-manage-vue.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-vue": "^2.0.13", "@bangdao/buse-components-element": "1.1.7", "@besovideo/webrtc-player": "^0.8.46", "@form-create/designer": "1.0.7", "@form-create/element-ui": "^2.5.26", "@logicflow/core": "1.1.29", "@logicflow/extension": "1.1.29", "@riophae/vue-treeselect": "^0.4.0", "@tweenjs/tween.js": "^21.0.0", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vue/composition-api": "^1.7.2", "axios": "^0.21.1", "core-js": "^3.4.3", "css-loader": "^1.0.1", "dayjs": "^1.11.9", "des.js": "^1.0.1", "echarts": "^5.4.3", "el-table-infinite-scroll": "^1.0.10", "element-china-area-data": "^6.1.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.15.7", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "js-base64": "^3.6.0", "js-cookie": "2.2.0", "js-file-download": "^0.4.12", "jsdiff-esm": "^1.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "number-precision": "^1.5.2", "print-js": "^1.6.0", "qs": "^6.10.3", "quill": "^2.0.0-dev.4", "screenfull": "^4.2.0", "signature_pad": "^4.0.10", "treeselect": "^1.1.1", "useless-files-webpack-plugin": "^1.0.1", "uuid": "^9.0.0", "video.js": "^8.9.0", "vue": "^2.6.14", "vue-count-to": "1.0.13", "vue-cropper": "0.4.9", "vue-demi": "^0.14.10", "vue-quill-editor": "3.0.6", "vue-router": "^3.0.7", "vuedraggable": "^2.24.3", "vuex": "^3.1.2", "vxe-table": "3.8.28", "vxe-table-plugin-export-xlsx": "2.2.2", "wl-gantt": "^1.0.6", "xe-utils": "^3.5.7", "xlsx": "^0.18.2", "yarn": "^1.22.17"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-jest": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-prettier": "^5.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-eslint": "^10.0.3", "compression-webpack-plugin": "1.1.12", "downloadjs": "^1.4.7", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^5.0.0", "html2canvas": "^1.1.4", "jspdf": "^2.3.1", "less": "^3.10.3", "less-loader": "^5.0.0", "lint-staged": "^9.4.3", "plop": "^2.5.3", "prettier": "^1.19.1", "script-ext-html-webpack-plugin": "^2.1.4", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "terser-webpack-plugin": "^4.2.3", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-loader": "14.2.2", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/prettier"], "rules": {"no-console": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "browserslist": ["> 1%", "last 2 versions"], "jest": {"preset": "@vue/cli-plugin-unit-jest"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}