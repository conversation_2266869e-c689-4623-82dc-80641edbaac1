export const ICON_SVGS = {
  "table-insert-row": `<svg viewbox="0 0 18 18">
      <g class="ql-fill ql-stroke ql-thin ql-transparent">
        <rect height="3" rx="0.5" ry="0.5" width="7" x="4.5" y="2.5"></rect>
        <rect height="3" rx="0.5" ry="0.5" width="7" x="4.5" y="12.5"></rect>
      </g>
      <rect class="ql-fill ql-stroke ql-thin" height="3" rx="0.5" ry="0.5" width="7" x="8.5" y="7.5"></rect>
      <polygon class="ql-fill ql-stroke ql-thin" points="4.5 11 2.5 9 4.5 7 4.5 11"></polygon>
      <line class="ql-stroke" x1="6" x2="4" y1="9" y2="9"></line>
    </svg>`,
  "table-insert-column": `<svg viewbox="0 0 18 18">
      <g class="ql-fill ql-transparent">
        <rect height="10" rx="1" ry="1" width="4" x="12" y="2"></rect>
        <rect height="10" rx="1" ry="1" width="4" x="2" y="2"></rect>
      </g>
      <path class="ql-fill" d="M11.354,4.146l-2-2a0.5,0.5,0,0,0-.707,0l-2,2A0.5,0.5,0,0,0,7,5H8V6a1,1,0,0,0,2,0V5h1A0.5,0.5,0,0,0,11.354,4.146Z"></path>
      <rect class="ql-fill" height="8" rx="1" ry="1" width="4" x="7" y="8"></rect>
    </svg>`,
  "table-delete-row": `<svg viewbox="0 0 18 18">
      <g class="ql-fill ql-stroke ql-thin ql-transparent">
        <rect height="3" rx="0.5" ry="0.5" width="7" x="4.5" y="2.5"></rect>
        <rect height="3" rx="0.5" ry="0.5" width="7" x="4.5" y="12.5"></rect>
      </g>
      <rect class="ql-fill ql-stroke ql-thin" height="3" rx="0.5" ry="0.5" width="7" x="8.5" y="7.5"></rect>
      <line class="ql-stroke ql-thin" x1="6.5" x2="3.5" y1="7.5" y2="10.5"></line>
      <line class="ql-stroke ql-thin" x1="3.5" x2="6.5" y1="7.5" y2="10.5"></line>
    </svg>`,
  "table-delete-column": `<svg viewbox="0 0 18 18">
      <g class="ql-fill ql-transparent">
        <rect height="10" rx="1" ry="1" width="4" x="2" y="6"></rect>
        <rect height="10" rx="1" ry="1" width="4" x="12" y="6"></rect>
      </g>
      <rect class="ql-fill" height="8" rx="1" ry="1" width="4" x="7" y="2"></rect>
      <path class="ql-fill" d="M9.707,13l1.146-1.146a0.5,0.5,0,0,0-.707-0.707L9,12.293,7.854,11.146a0.5,0.5,0,0,0-.707.707L8.293,13,7.146,14.146a0.5,0.5,0,1,0,.707.707L9,13.707l1.146,1.146a0.5,0.5,0,0,0,.707-0.707Z"></path>
    </svg>`,
};
