<template>
  <div>
    <div class="editor"></div>
  </div>
</template>

<script>
import Quill from "quill";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
const titleConfig = {
  "ql-bold": "加粗",
  "ql-color": "颜色",
  "ql-font": "字体",
  "ql-code": "插入代码",
  "ql-italic": "斜体",
  "ql-link": "添加链接",
  "ql-background": "颜色",
  "ql-size": "字体大小",
  "ql-strike": "删除线",
  "ql-script": "上标/下标",
  "ql-underline": "下划线",
  "ql-blockquote": "引用",
  "ql-header": "标题",
  "ql-indent": "缩进",
  "ql-list": "列表",
  "ql-align": "文本对齐",
  "ql-direction": "文本方向",
  "ql-code-block": "代码块",
  "ql-formula": "公式",
  "ql-image": "图片",
  "ql-video": "视频",
  "ql-clean": "清除字体样式",
  "ql-upload": "文件",
  "ql-table": "插入表格",
  "ql-table-insert-row": "插入行",
  "ql-table-insert-column": "插入列",
  "ql-table-delete-row": "删除行",
  "ql-table-delete-column": "删除列",
};
// 字体白名单
const Font = Quill.import("formats/font");
Font.whitelist = [
  "SimSun",
  "SimHei",
  "Microsoft-YaHei",
  "KaiTi",
  "FangSong",
  "Arial",
  "Times-New-Roman",
  "sans-serif",
];
Quill.register(Font, true);

export default {
  name: "Editor",
  props: {
    value: {},
  },
  data() {
    return {
      quill: null,
      options: {
        theme: "snow",
        modules: {
          toolbar: {
            container: [
              ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
              ["blockquote", "code-block"], // 引用  代码块
              //   [{ header: 1 }, { header: 2 }], // 1、2 级标题
              [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
              [{ script: "sub" }, { script: "super" }], // 上标/下标
              [{ indent: "-1" }, { indent: "+1" }], // 缩进
              // [{'direction': 'rtl'}],                         // 文本方向
              [{ size: ["small", false, "large", "huge"] }], // 字体大小
              [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
              [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
              [
                {
                  font: [
                    "SimSun",
                    "SimHei",
                    "Microsoft-YaHei",
                    "KaiTi",
                    "FangSong",
                    "Arial",
                    "Times-New-Roman",
                    "sans-serif",
                  ],
                },
              ], // 字体种类
              [{ align: [] }], // 对齐方式
              ["clean"], // 清除文本格式
              // ["link","image"], // 链接、图片、视频
              [
                { table: "TD" },
                { "table-insert-row": "TIR" },
                { "table-insert-column": "TIC" },
                { "table-delete-row": "TDR" },
                { "table-delete-column": "TDC" },
              ],
            ],
            handlers: {
              table: function(val) {
                this.quill.getModule("table").insertTable(3, 3);
              },
              "table-insert-row": function() {
                this.quill.getModule("table").insertRowBelow();
              },
              "table-insert-column": function() {
                this.quill.getModule("table").insertColumnRight();
              },
              "table-delete-row": function() {
                this.quill.getModule("table").deleteRow();
              },
              "table-delete-column": function() {
                this.quill.getModule("table").deleteColumn();
              },
            },
          },
          table: true,
        },
        // readOnly: true, //是否只读
        placeholder: "",
      },
    };
  },
  methods: {
    addQuillTitle() {
      const oToolBar = document.querySelector(".ql-toolbar");
      const aButton = oToolBar.querySelectorAll("button");
      const aSelect = oToolBar.querySelectorAll("select");
      aButton.forEach(function(item) {
        if (item.className === "ql-script") {
          item.value === "sub" ? (item.title = "下标") : (item.title = "上标");
        } else if (item.className === "ql-indent") {
          item.value === "+1"
            ? (item.title = "向右缩进")
            : (item.title = "向左缩进");
        } else {
          item.title = titleConfig[item.classList[0]];
        }
      });
      aSelect.forEach(function(item) {
        item.parentNode.title = titleConfig[item.classList[0]];
      });
    },
    getContentData() {
      return this.quill.getContents();
    },
    /**
     * class转style，适用于邮件等场景
     * 用法：this.fontClassToStyle(html)
     * 建议在需要发送邮件或导出时调用，不要影响编辑器内部内容
     */
    fontClassToStyle(html) {
      const fontMap = {
        "ql-font-SimSun": "font-family: SimSun, serif;",
        "ql-font-SimHei": "font-family: SimHei, sans-serif;",
        "ql-font-Microsoft-YaHei": "font-family: Microsoft YaHei, sans-serif;",
        "ql-font-KaiTi": "font-family: KaiTi, serif;",
        "ql-font-FangSong": "font-family: FangSong, serif;",
        "ql-font-Arial": "font-family: Arial, sans-serif;",
        "ql-font-Times-New-Roman": "font-family: Times New Roman, serif;",
        "ql-font-sans-serif": "font-family: sans-serif;",
      };
      return html.replace(
        /class="([^"]*?ql-font-[^"]*?)"/g,
        (match, classNames) => {
          let style = "";
          classNames.split(" ").forEach((cls) => {
            if (fontMap[cls]) style += fontMap[cls];
          });
          if (style) {
            // 保留原有class（如果有其他class），并加上style
            return match + ` style="${style}"`;
          }
          return match;
        }
      );
    },
  },
  mounted() {
    const dom = this.$el.querySelector(".editor");
    this.quill = new Quill(dom, this.options);

    // this.quill.setContents(this.value) //detla格式数据
    // this.quill.setText(this.value) //纯文本
    this.quill.clipboard.dangerouslyPasteHTML(0, this.value); // html格式数据
    this.quill.on("text-change", () => {
      // console.log(this.quill.getContents())//detla格式数据
      // this.$emit('contentData', this.quill.getContents())
      // console.log(this.quill.root.innerHTML)//html格式数据
      this.$emit("contentData", this.quill.root.innerHTML);
    });
    this.$el.querySelector(
      ".ql-table-insert-row"
    ).innerHTML = `<svg t="1591862376726" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6306" width="18" height="200"><path d="M500.8 604.779L267.307 371.392l-45.227 45.27 278.741 278.613L779.307 416.66l-45.248-45.248z" p-id="6307"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-insert-column"
    ).innerHTML = `<svg t="1591862238963" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6509" width="18" height="200"><path d="M593.450667 512.128L360.064 278.613333l45.290667-45.226666 278.613333 278.762666L405.333333 790.613333l-45.226666-45.269333z" p-id="6510"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-delete-row"
    ).innerHTML = `<svg t="1591862253524" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6632" width="18" height="200"><path d="M500.8 461.909333L267.306667 695.296l-45.226667-45.269333 278.741333-278.613334L779.306667 650.026667l-45.248 45.226666z" p-id="6633"></path></svg>`;
    this.$el.querySelector(
      ".ql-table-delete-column"
    ).innerHTML = `<svg t="1591862261059" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6755" width="18" height="200"><path d="M641.28 278.613333l-45.226667-45.226666-278.634666 278.762666 278.613333 278.485334 45.248-45.269334-233.365333-233.237333z" p-id="6756"></path></svg>`;
    this.addQuillTitle();
  },
  activated() {
    this.quill.setContents({});
  },
};
</script>
<style lang="less">
.editor {
  line-height: normal !important;
  height: 192px;
}
/*.el-upload {*/
/*  display: none;*/
/*}*/

.ql-container {
  max-height: 160px;
  overflow: auto;
  .ql-tooltip {
    left: 0 !important;
  }
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}
.ql-editor {
  /* overflow: hidden; */
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimSun"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimSun"]::before {
  content: "宋体";
  font-family: "SimSun", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimHei"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimHei"]::before {
  content: "黑体";
  font-family: "SimHei", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Microsoft-YaHei"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Microsoft-YaHei"]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="KaiTi"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="KaiTi"]::before {
  content: "楷体";
  font-family: "KaiTi", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangSong"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangSong"]::before {
  content: "仿宋";
  font-family: "FangSong", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before {
  content: "Arial";
  font-family: "Arial", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Times-New-Roman"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Times-New-Roman"]::before {
  content: "Times New Roman";
  font-family: "Times New Roman", serif;
}

.ql-font-SimSun {
  font-family: "SimSun", serif;
}
.ql-font-SimHei {
  font-family: "SimHei", sans-serif;
}
.ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-font-KaiTi {
  font-family: "KaiTi", serif;
}
.ql-font-FangSong {
  font-family: "FangSong", serif;
}
.ql-font-Arial {
  font-family: "Arial", sans-serif;
}
.ql-font-Times-New-Roman {
  font-family: "Times New Roman", serif;
}
.ql-font-sans-serif {
  font-family: sans-serif;
}
</style>
